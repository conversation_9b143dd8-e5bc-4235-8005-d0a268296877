# Mock Tool 
A fake AP and fake tool hosted in a Docker container to facilitate RealTime development, debugging and testing.
There is also a script that can reverse-encode a given .msra file into (approximately) the original binary data emitted by the tool.

## Environment Setup
### Python 3.8.10
```
pip install -r requirements.txt
```
### Docker

## Test Data
- sample_1.msra = \\DS923-01\ms_rd\RD_13_MachSensing_切削資料\正修-展品\TEST_A182_20250328-155757_20250328-155846第五次精銑一半G02G03S15000F3480.msra
- sample_2.msra = \machsync_onedrive\RD03_Software\Overview-to-MachRadar-file\Before mvc\.msra-file\MachRadar RealTime Pro\Record_20250509-164131.msra ("$" to mark tare are removed for easy parsing)
- sample_3.msra = \\DS923-01\ms_rd\RD_13_MachSensing_切削資料\Record_20250509正修實測\實切_5_A132_20250509-140706_20250509-141207.msra

## Generate Binary Data from .msra
Current .msra files lack some information (linear, etc) required for re-encoding. Temporary solution: the complete information of a tool is stored in sample_x_tool_info. 
```bash
python convert_msra_to_bin.py [msra file] [tool info file] [output file]
```

## Run RealTime with AP and Tool Simulation
### Point RealTime to Fake AP
In the ``.env`` file of the RealTime project, change the network device url to:
```txt
NETWORK_DEVICE_URL=http://127.0.0.1:8000
```
In order to connect it to the Fake AP.

### Run Container
```
docker compose up --build
```
A few notes (to be optimized in the future):
1. The container is hardcoded in docker-compose.yml to read from samples/sample_1.dat; change it if you want to read from another file.
2. The host addresses and ports are also hardcoded in docker-compose.yml.

### Add Fake Tool 
In the RealTime GUI, add the fake tool by entering its MAC ```AA:BB:CC:DD:EE:FF```. 

### Connect to Tool
Proceed as usual.