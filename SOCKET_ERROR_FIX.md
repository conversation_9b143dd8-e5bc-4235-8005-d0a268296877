# Socket 錯誤修復說明

## 問題描述
在 Windows 環境下運行時出現以下錯誤：
```
Socket 關閉異常: [WinError 10057] 不允許傳送或接收資料的要求，因為通訊端並未連線，而且 (在資料包通訊端使用 sendto 呼叫進行傳送時) 並未提供位址。
```

## 錯誤原因
WinError 10057 (WSAENOTCONN) 是 Windows 特有的 socket 錯誤，表示：
- Socket 連接已經斷開
- 程式仍然嘗試在已關閉的 socket 上發送或接收數據
- 原始代碼只捕獲了 `BrokenPipeError` 和 `ConnectionResetError`，沒有處理 Windows 的 `OSError`

## 修復內容

### 1. replay_socket_server.py
在 `_stream` 函數中添加了 `OSError` 異常處理：

```python
except (BrokenPipeError, ConnectionResetError, OSError) as e:
    # Client closed the connection – simply return to let caller handle log.
    # OSError covers Windows-specific socket errors like WinError 10057
    print(f"[replay] Connection error: {e}")
    return
```

### 2. mock_ap_server.py
在 HTTP 處理方法中添加了錯誤處理：

```python
# 在 _send, do_GET, do_POST 方法中添加：
except (BrokenPipeError, ConnectionResetError, OSError) as e:
    print(f"[mock-ap] Error: {e}")
    return
```

## 測試方法

### 1. 運行測試腳本
```bash
python test_socket_connection.py
```

### 2. 手動測試
啟動服務器：
```bash
# 終端 1: 啟動 Mock AP 服務器
python mock_ap_server.py

# 終端 2: 啟動 Replay 服務器
python replay_socket_server.py samples/sample_1.dat
```

測試連接：
```bash
# 終端 3: 運行測試
python test_socket_connection.py
```

### 3. Docker 測試
```bash
docker compose up --build
```

## 修復效果
- ✅ 正確處理 Windows socket 斷線錯誤
- ✅ 避免程式崩潰
- ✅ 提供有意義的錯誤日誌
- ✅ 服務器可以繼續處理新連接
- ✅ 跨平台兼容性（Windows/Linux/macOS）

## 注意事項
1. 這個修復處理的是**正常的連接斷開**情況
2. 如果錯誤持續出現，可能需要檢查：
   - 網路連接狀態
   - 防火牆設置
   - 客戶端程式的連接邏輯
3. 錯誤日誌會顯示具體的錯誤信息，有助於進一步診斷

## 相關文件
- `replay_socket_server.py` - TCP 數據重播服務器
- `mock_ap_server.py` - Mock AP HTTP 服務器  
- `test_socket_connection.py` - 連接測試腳本
- `docker-compose.yml` - Docker 容器配置
