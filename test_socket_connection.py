#!/usr/bin/env python3
"""test_socket_connection.py

Simple test script to verify socket connection handling and error recovery.
This script tests both the replay server and mock AP server for proper
error handling when connections are abruptly closed.
"""
import socket
import time
import threading
import requests
from pathlib import Path


def test_replay_server_connection():
    """Test connection to replay server and simulate abrupt disconnection."""
    print("[test] Testing replay server connection...")
    
    try:
        # Connect to replay server
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(("127.0.0.1", 1333))
        print("[test] Connected to replay server")
        
        # Read some data
        data = sock.recv(1024)
        print(f"[test] Received {len(data)} bytes")
        
        # Abruptly close connection without proper shutdown
        sock.close()
        print("[test] Abruptly closed connection")
        
    except ConnectionRefusedError:
        print("[test] Replay server not running - start it first")
    except Exception as e:
        print(f"[test] Error testing replay server: {e}")


def test_mock_ap_connection():
    """Test connection to mock AP server."""
    print("[test] Testing mock AP server connection...")
    
    try:
        # Test GET request
        response = requests.get("http://127.0.0.1:8000/cgi-bin/luci/", timeout=5)
        print(f"[test] GET response status: {response.status_code}")
        
        # Test POST request
        response = requests.post("http://127.0.0.1:8000/cgi-bin/luci/", 
                               json={"test": "data"}, timeout=5)
        print(f"[test] POST response status: {response.status_code}")
        
        # Test statistics endpoint
        response = requests.get("http://127.0.0.1:8000/cgi-bin/luci/admin/statistics?id=0&status=1", 
                              timeout=5)
        print(f"[test] Statistics response status: {response.status_code}")
        print(f"[test] Statistics response: {response.text}")
        
    except requests.exceptions.ConnectionError:
        print("[test] Mock AP server not running - start it first")
    except Exception as e:
        print(f"[test] Error testing mock AP server: {e}")


def test_multiple_connections():
    """Test multiple simultaneous connections to stress test error handling."""
    print("[test] Testing multiple connections...")
    
    def connect_and_disconnect():
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(("127.0.0.1", 1333))
            time.sleep(0.1)  # Brief connection
            sock.close()
        except Exception as e:
            print(f"[test] Connection error: {e}")
    
    # Create multiple threads to test concurrent connections
    threads = []
    for i in range(5):
        thread = threading.Thread(target=connect_and_disconnect)
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    print("[test] Multiple connection test completed")


def main():
    print("Socket Connection Test")
    print("=" * 50)
    
    # Check if sample data exists
    sample_file = Path("samples/sample_1.dat")
    if not sample_file.exists():
        print("[test] Warning: sample_1.dat not found - replay server may not work")
    
    print("\n1. Testing Mock AP Server...")
    test_mock_ap_connection()
    
    print("\n2. Testing Replay Server...")
    test_replay_server_connection()
    
    print("\n3. Testing Multiple Connections...")
    test_multiple_connections()
    
    print("\n[test] All tests completed")
    print("\nTo run the servers:")
    print("  python mock_ap_server.py")
    print("  python replay_socket_server.py samples/sample_1.dat")


if __name__ == "__main__":
    main()
