#!/usr/bin/env python3
"""mock_ap_server.py

Very small LuCI-style mock access-point server for local testing.
It satisfies the exact HTTP requests made by `network_device_manager.py` so you
can run the full discovery workflow without real hardware.

Supported endpoints
-------------------
1. GET  /cgi-bin/luci/                       → dummy HTML (sets a cookie)
2. POST /cgi-bin/luci/                       → 200 OK (login success)
3. GET  /cgi-bin/luci/admin/statistics?id=0&status=1
                                            → JSON with ARP table data

Usage
-----
python mock_ap_server.py [--host 0.0.0.0] [--port 8000] [--devices DEV_JSON]

If *DEV_JSON* is given it must be a JSON file containing an array of objects
with at least `bssid` (MAC) and `ip` fields.  If omitted a single dummy device
is returned.

The server speaks plain HTTP.  Point `NETWORK_DEVICE_URL` in your config to
`http://<host>:<port>` or use an ngrok/tcp tunnel to expose it.
"""
from __future__ import annotations

import argparse
import json
import pathlib
import ssl
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from typing import List, Dict

DEFAULT_DEVICE_LIST = [
    {"bssid": "AA:BB:CC:DD:EE:FF", "ip": "127.0.0.1"},
]


def load_devices(path: pathlib.Path | None) -> List[Dict[str, str]]:
    if path is None:
        return DEFAULT_DEVICE_LIST
    try:
        with path.open("rt", encoding="utf-8") as fp:
            data = json.load(fp)
        # ensure proper shape
        devices = []
        for d in data:
            if not (isinstance(d, dict) and "bssid" in d and "ip" in d):
                raise ValueError("Each device must be object with 'bssid' and 'ip'")
            devices.append({"bssid": d["bssid"], "ip": d["ip"]})
        return devices
    except Exception as e:
        raise SystemExit(f"Failed to load devices from {path}: {e}")


class MockAPHandler(BaseHTTPRequestHandler):
    server_version = "MockAP/0.1"

    def _send(self, status: int, body: bytes, content_type: str = "text/html") -> None:
        try:
            self.send_response(status)
            self.send_header("Content-Type", content_type)
            self.send_header("Content-Length", str(len(body)))
            self.end_headers()
            self.wfile.write(body)
        except (BrokenPipeError, ConnectionResetError, OSError) as e:
            # Client disconnected during response - log and continue
            print(f"[mock-ap] Client disconnected during response: {e}")
            return

    # GET ------------------------------------------------------------------
    def do_GET(self):  # noqa: N802
        try:
            parsed = urlparse(self.path)
            if parsed.path == "/cgi-bin/luci/":
                # login page
                self._send(200, b"<html><body><h1>LuCI Mock Login</h1></body></html>")
            elif parsed.path == "/cgi-bin/luci/admin/statistics":
                qs = parse_qs(parsed.query)
                if qs.get("id") == ["0"] and qs.get("status") == ["1"]:
                    payload = json.dumps({"client": self.server.devices}).encode()
                    self._send(200, payload, content_type="application/json")
                else:
                    self._send(404, b"not found")
            else:
                self._send(404, b"not found")
        except (BrokenPipeError, ConnectionResetError, OSError) as e:
            print(f"[mock-ap] Error in GET handler: {e}")
            return

    # POST -----------------------------------------------------------------
    def do_POST(self):  # noqa: N802
        try:
            parsed = urlparse(self.path)
            if parsed.path == "/cgi-bin/luci/":
                # consume request body but ignore
                content_length = int(self.headers.get("Content-Length", 0))
                if content_length > 0:
                    _ = self.rfile.read(content_length)
                # Success page WITHOUT the combination of 'Login', 'Username', 'Password'
                self._send(200, b"{\"success\":true}", content_type="application/json")
            else:
                self._send(404, b"not found")
        except (BrokenPipeError, ConnectionResetError, OSError) as e:
            print(f"[mock-ap] Error in POST handler: {e}")
            return

    # silence logging
    def log_message(self, fmt, *args):  # noqa: N802
        # Intentionally unused parameters for overriding parent method
        pass


# ---------------------------------------------------------------------------
# CLI
# ---------------------------------------------------------------------------

def main() -> None:
    parser = argparse.ArgumentParser(description="Mock LuCI access-point server")
    parser.add_argument("--host", default="0.0.0.0", help="bind address (default 0.0.0.0)")
    parser.add_argument("--port", type=int, default=8000, help="TCP port (default 8000)")
    parser.add_argument("--devices", type=pathlib.Path, help="JSON file with device list")
    parser.add_argument("--tls", action="store_true", help="Enable self-signed HTTPS (experimental)")
    args = parser.parse_args()

    devices = load_devices(args.devices)
    httpd: HTTPServer = HTTPServer((args.host, args.port), MockAPHandler)
    httpd.devices = devices  # type: ignore[attr-defined]

    if args.tls:
        # Generate self-signed cert with `openssl req ...` and put as cert.pem / key.pem
        cert = pathlib.Path("cert.pem")
        key = pathlib.Path("key.pem")
        if not (cert.exists() and key.exists()):
            raise SystemExit("cert.pem/key.pem not found for TLS mode")
        httpd.socket = ssl.wrap_socket(httpd.socket, certfile=str(cert), keyfile=str(key), server_side=True)
        scheme = "https"
    else:
        scheme = "http"

    print(f"Mock AP server running on {scheme}://{args.host}:{args.port}")
    print(f"Serving {len(devices)} device(s)")
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("Shutting down…")


if __name__ == "__main__":
    main()

